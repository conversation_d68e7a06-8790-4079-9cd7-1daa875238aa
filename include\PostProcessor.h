#ifndef POSTPROCESSOR_H
#define POSTPROCESSOR_H

#include <GL/glew.h>
#include <glm/glm.hpp>
#include <memory>
#include <vector>

class Shader;

/**
 * @brief Énumération des effets de post-traitement disponibles
 */
enum class PostProcessEffect {
    NONE,           // Aucun effet
    BLUR,           // Flou gaussien
    BLOOM,          // Effet bloom
    EDGE_DETECTION, // Détection de contours
    SHARPEN,        // Accentuation
    GRAYSCALE,      // Noir et blanc
    SEPIA,          // Effet sépia
    INVERT,         // Inversion des couleurs
    VIGNETTE        // Effet vignette
};

/**
 * @brief Classe PostProcessor - Système de post-traitement avec framebuffers
 * 
 * Cette classe implémente un système de post-traitement utilisant des
 * Framebuffer Objects (FBO) pour appliquer des effets visuels à la scène
 * rendue. Elle supporte plusieurs effets et permet de les combiner.
 */
class PostProcessor {
private:
    // === Framebuffers ===
    GLuint sceneFBO;            // Framebuffer principal pour la scène
    GLuint sceneColorTexture;   // Texture couleur de la scène
    GLuint sceneDepthTexture;   // Texture de profondeur de la scène
    
    GLuint pingpongFBO[2];      // Framebuffers pour ping-pong (blur, bloom)
    GLuint pingpongTextures[2]; // Textures pour ping-pong
    
    GLuint brightnessFBO;       // Framebuffer pour extraire les zones lumineuses
    GLuint brightnessTexture;   // Texture des zones lumineuses
    
    // === Géométrie pour le rendu plein écran ===
    GLuint quadVAO, quadVBO;
    
    // === Shaders ===
    std::unique_ptr<Shader> screenShader;      // Shader de base pour affichage
    std::unique_ptr<Shader> blurShader;        // Shader de flou
    std::unique_ptr<Shader> bloomShader;       // Shader de bloom
    std::unique_ptr<Shader> brightnessShader;  // Shader d'extraction de luminosité
    std::unique_ptr<Shader> combineShader;     // Shader de combinaison
    
    // === Paramètres ===
    int screenWidth, screenHeight;
    bool isInitialized;
    
    // Vertices pour le quad plein écran
    static const float quadVertices[24];

public:
    /**
     * @brief Constructeur par défaut
     */
    PostProcessor();
    
    /**
     * @brief Destructeur - Libère les ressources OpenGL
     */
    ~PostProcessor();
    
    /**
     * @brief Constructeur de copie supprimé (ressource unique)
     */
    PostProcessor(const PostProcessor&) = delete;
    
    /**
     * @brief Opérateur d'assignation supprimé (ressource unique)
     */
    PostProcessor& operator=(const PostProcessor&) = delete;

    /**
     * @brief Initialise le système de post-traitement
     * @param width Largeur de l'écran
     * @param height Hauteur de l'écran
     * @return true si l'initialisation réussit, false sinon
     */
    bool Initialize(int width, int height);
    
    /**
     * @brief Redimensionne les framebuffers
     * @param width Nouvelle largeur
     * @param height Nouvelle hauteur
     */
    void Resize(int width, int height);
    
    /**
     * @brief Commence le rendu vers le framebuffer de scène
     */
    void BeginSceneRender();
    
    /**
     * @brief Termine le rendu de scène et applique les effets
     * @param effects Liste des effets à appliquer
     */
    void EndSceneRender(const std::vector<PostProcessEffect>& effects = {});
    
    /**
     * @brief Applique un effet spécifique
     * @param effect Effet à appliquer
     * @param intensity Intensité de l'effet (0.0 à 1.0)
     */
    void ApplyEffect(PostProcessEffect effect, float intensity = 1.0f);
    
    /**
     * @brief Rend la texture finale à l'écran
     */
    void RenderToScreen();
    
    /**
     * @brief Vérifie si le système est initialisé
     * @return true si initialisé, false sinon
     */
    bool IsInitialized() const { return isInitialized; }
    
    /**
     * @brief Retourne l'ID de la texture de scène
     * @return ID de la texture
     */
    GLuint GetSceneTexture() const { return sceneColorTexture; }

private:
    /**
     * @brief Crée les framebuffers nécessaires
     * @return true si la création réussit, false sinon
     */
    bool CreateFramebuffers();
    
    /**
     * @brief Crée la géométrie du quad plein écran
     */
    void SetupQuadGeometry();
    
    /**
     * @brief Charge tous les shaders nécessaires
     * @return true si le chargement réussit, false sinon
     */
    bool LoadShaders();
    
    /**
     * @brief Applique un flou gaussien
     * @param horizontal true pour flou horizontal, false pour vertical
     * @param intensity Intensité du flou
     */
    void ApplyBlur(bool horizontal, float intensity = 1.0f);
    
    /**
     * @brief Applique l'effet bloom
     * @param intensity Intensité du bloom
     */
    void ApplyBloom(float intensity = 1.0f);
    
    /**
     * @brief Extrait les zones lumineuses de la scène
     * @param threshold Seuil de luminosité
     */
    void ExtractBrightness(float threshold = 1.0f);
    
    /**
     * @brief Combine deux textures
     * @param texture1 Première texture
     * @param texture2 Deuxième texture
     * @param blend Facteur de mélange
     */
    void CombineTextures(GLuint texture1, GLuint texture2, float blend = 0.5f);
    
    /**
     * @brief Rend un quad plein écran avec une texture
     * @param textureID ID de la texture à rendre
     */
    void RenderQuad(GLuint textureID);
    
    /**
     * @brief Libère les ressources OpenGL
     */
    void Cleanup();
};

/**
 * @brief Structure pour les paramètres de post-traitement
 */
struct PostProcessParams {
    float blurIntensity = 1.0f;     // Intensité du flou
    float bloomThreshold = 1.0f;    // Seuil pour le bloom
    float bloomIntensity = 0.5f;    // Intensité du bloom
    float vignetteStrength = 0.3f;  // Force de la vignette
    float saturation = 1.0f;        // Saturation des couleurs
    float contrast = 1.0f;          // Contraste
    float brightness = 0.0f;        // Luminosité
};

#endif // POSTPROCESSOR_H
