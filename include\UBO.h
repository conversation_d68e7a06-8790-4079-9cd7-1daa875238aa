#ifndef UBO_H
#define UBO_H

#include <GL/glew.h>
#include <glm/glm.hpp>
#include <string>

/**
 * @brief Classe UBO - Uniform Buffer Object pour optimiser les performances
 * 
 * Cette classe encapsule la gestion des Uniform Buffer Objects (UBO) d'OpenGL
 * pour permettre un passage efficace de données uniformes aux shaders.
 * Les UBO permettent de grouper plusieurs uniforms et de les mettre à jour
 * en une seule opération, réduisant les appels OpenGL.
 */
class UBO {
private:
    GLuint bufferID;        // ID du buffer OpenGL
    GLuint bindingPoint;    // Point de liaison du buffer
    size_t bufferSize;      // Taille du buffer en octets
    bool isInitialized;     // État d'initialisation

public:
    /**
     * @brief Constructeur par défaut
     */
    UBO();
    
    /**
     * @brief Destructeur - Libère les ressources OpenGL
     */
    ~UBO();
    
    /**
     * @brief Constructeur de copie supprimé (ressource unique)
     */
    UBO(const UBO&) = delete;
    
    /**
     * @brief Opérateur d'assignation supprimé (ressource unique)
     */
    UBO& operator=(const UBO&) = delete;
    
    /**
     * @brief Constructeur de déplacement
     */
    UBO(UBO&& other) noexcept;
    
    /**
     * @brief Opérateur d'assignation de déplacement
     */
    UBO& operator=(UBO&& other) noexcept;

    /**
     * @brief Initialise l'UBO avec une taille donnée
     * @param size Taille du buffer en octets
     * @param binding Point de liaison (0-based)
     * @param usage Type d'usage OpenGL (GL_STATIC_DRAW, GL_DYNAMIC_DRAW, etc.)
     * @return true si l'initialisation réussit, false sinon
     */
    bool Initialize(size_t size, GLuint binding, GLenum usage = GL_DYNAMIC_DRAW);
    
    /**
     * @brief Met à jour les données du buffer
     * @param data Pointeur vers les données
     * @param size Taille des données en octets
     * @param offset Décalage dans le buffer (défaut: 0)
     */
    void UpdateData(const void* data, size_t size, size_t offset = 0);
    
    /**
     * @brief Lie l'UBO au point de liaison spécifié
     */
    void Bind() const;
    
    /**
     * @brief Délie l'UBO
     */
    void Unbind() const;
    
    /**
     * @brief Lie l'UBO à un shader program
     * @param programID ID du programme shader
     * @param uniformBlockName Nom du bloc uniform dans le shader
     */
    void BindToShader(GLuint programID, const std::string& uniformBlockName) const;
    
    /**
     * @brief Retourne l'ID du buffer OpenGL
     * @return ID du buffer
     */
    GLuint GetBufferID() const { return bufferID; }
    
    /**
     * @brief Retourne le point de liaison
     * @return Point de liaison
     */
    GLuint GetBindingPoint() const { return bindingPoint; }
    
    /**
     * @brief Retourne la taille du buffer
     * @return Taille en octets
     */
    size_t GetSize() const { return bufferSize; }
    
    /**
     * @brief Vérifie si l'UBO est initialisé
     * @return true si initialisé, false sinon
     */
    bool IsInitialized() const { return isInitialized; }

private:
    /**
     * @brief Libère les ressources OpenGL
     */
    void Cleanup();
};

/**
 * @brief Structure pour les données de caméra et projection
 * 
 * Cette structure est alignée pour être compatible avec les
 * standards d'alignement des UBO (std140).
 */
struct CameraUBOData {
    alignas(16) glm::mat4 view;         // Matrice de vue
    alignas(16) glm::mat4 projection;   // Matrice de projection
    alignas(16) glm::vec3 viewPos;      // Position de la caméra
    alignas(4)  float padding1;         // Padding pour alignement
    alignas(16) glm::vec3 viewDir;      // Direction de vue
    alignas(4)  float padding2;         // Padding pour alignement
};

/**
 * @brief Structure pour les données de transformation d'objet
 * 
 * Cette structure contient les matrices de transformation
 * pour un objet spécifique.
 */
struct TransformUBOData {
    alignas(16) glm::mat4 model;        // Matrice de modèle
    alignas(16) glm::mat4 normalMatrix; // Matrice des normales (inverse transposée)
};

/**
 * @brief Structure pour les données d'éclairage
 * 
 * Cette structure contient les paramètres d'éclairage
 * utilisés par les shaders.
 */
struct LightingUBOData {
    alignas(16) glm::vec3 lightPos;     // Position de la lumière
    alignas(4)  float padding1;
    alignas(16) glm::vec3 lightColor;   // Couleur de la lumière
    alignas(4)  float lightIntensity;   // Intensité de la lumière
    alignas(16) glm::vec3 ambientColor; // Couleur ambiante
    alignas(4)  float ambientStrength;  // Force de l'éclairage ambiant
};

/**
 * @brief Classe utilitaire pour gérer les UBO courants
 * 
 * Cette classe fournit une interface simplifiée pour gérer
 * les UBO les plus couramment utilisés dans le projet.
 */
class UBOManager {
private:
    static UBO cameraUBO;       // UBO pour caméra/projection
    static UBO transformUBO;    // UBO pour transformations
    static UBO lightingUBO;     // UBO pour éclairage
    static bool initialized;    // État d'initialisation

public:
    /**
     * @brief Initialise tous les UBO
     * @return true si l'initialisation réussit, false sinon
     */
    static bool Initialize();
    
    /**
     * @brief Nettoie tous les UBO
     */
    static void Cleanup();
    
    /**
     * @brief Met à jour les données de caméra
     * @param data Données de caméra
     */
    static void UpdateCameraData(const CameraUBOData& data);
    
    /**
     * @brief Met à jour les données de transformation
     * @param data Données de transformation
     */
    static void UpdateTransformData(const TransformUBOData& data);
    
    /**
     * @brief Met à jour les données d'éclairage
     * @param data Données d'éclairage
     */
    static void UpdateLightingData(const LightingUBOData& data);
    
    /**
     * @brief Lie tous les UBO aux shaders
     * @param programID ID du programme shader
     */
    static void BindToShader(GLuint programID);
    
    /**
     * @brief Vérifie si le manager est initialisé
     * @return true si initialisé, false sinon
     */
    static bool IsInitialized() { return initialized; }

    // Points de liaison des UBO (doivent correspondre aux shaders)
    static constexpr GLuint CAMERA_BINDING_POINT = 0;
    static constexpr GLuint TRANSFORM_BINDING_POINT = 1;
    static constexpr GLuint LIGHTING_BINDING_POINT = 2;
};

#endif // UBO_H
