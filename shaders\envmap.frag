#version 330 core

// === Sortie ===
out vec4 FragColor;

// === Entrées du vertex shader ===
in vec3 FragPos;
in vec3 Normal;
in vec2 TexCoords;
in vec3 ViewPos;
in vec3 ReflectDir;

// === Uniforms pour l'éclairage ===
uniform vec3 lightPos;
uniform vec3 lightColor;
uniform vec3 objectColor;

// === Uniforms pour les textures ===
uniform sampler2D texture_diffuse1;
uniform samplerCube environmentMap;
uniform bool useTexture;
uniform bool useEnvironmentMapping;
uniform float reflectivity; // Facteur de réflectivité (0.0 à 1.0)

void main() {
    // === Couleur de base de l'objet ===
    vec3 baseColor;
    if (useTexture) {
        baseColor = texture(texture_diffuse1, TexCoords).rgb;
    } else {
        baseColor = objectColor;
    }
    
    // === Calcul de l'éclairage Phong ===
    vec3 norm = normalize(Normal);
    vec3 lightDir = normalize(lightPos - FragPos);
    vec3 viewDir = normalize(ViewPos - FragPos);
    
    // Composante ambiante
    float ambientStrength = 0.1;
    vec3 ambient = ambientStrength * lightColor;
    
    // Composante diffuse
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * lightColor;
    
    // Composante spéculaire
    float specularStrength = 0.5;
    vec3 reflectDir = reflect(-lightDir, norm);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32);
    vec3 specular = specularStrength * spec * lightColor;
    
    // Couleur avec éclairage
    vec3 litColor = (ambient + diffuse + specular) * baseColor;
    
    // === Environment mapping ===
    vec3 finalColor = litColor;
    
    if (useEnvironmentMapping) {
        // Échantillonne la texture d'environnement
        vec3 envColor = texture(environmentMap, ReflectDir).rgb;
        
        // Mélange la couleur de base avec la réflexion
        finalColor = mix(litColor, envColor, reflectivity);
    }
    
    FragColor = vec4(finalColor, 1.0);
}
