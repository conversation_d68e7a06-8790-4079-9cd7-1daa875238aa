#version 330 core

// === Sortie ===
out vec4 FragColor;

// === Entrées du vertex shader ===
in vec3 FragPos;        // Position du fragment dans l'espace monde
in vec3 Normal;         // Normale interpolée
in vec2 TexCoords;      // Coordonnées de texture
in vec3 ViewPos;        // Position de la caméra

// === Uniforms traditionnels (compatibilité) ===
uniform vec3 lightPos;
uniform vec3 lightColor;
uniform vec3 objectColor;
uniform sampler2D texture_diffuse1;
uniform bool useTexture;

// === UBO pour l'éclairage ===
layout (std140) uniform LightingData {
    vec3 uLightPos;
    float padding1;
    vec3 uLightColor;
    float uLightIntensity;
    vec3 uAmbientColor;
    float uAmbientStrength;
};

void main() {
    // === Paramètres d'éclairage ===
    // Utilise les UBO si disponibles, sinon les uniforms traditionnels
    vec3 finalLightPos = (length(uLightPos) > 0.0) ? uLightPos : lightPos;
    vec3 finalLightColor = (length(uLightColor) > 0.0) ? uLightColor : lightColor;
    float lightIntensity = (uLightIntensity > 0.0) ? uLightIntensity : 1.0;
    vec3 ambientColor = (length(uAmbientColor) > 0.0) ? uAmbientColor : finalLightColor;
    float ambientStrength = (uAmbientStrength > 0.0) ? uAmbientStrength : 0.1;
    
    // === Couleur de base de l'objet ===
    vec3 baseColor;
    if (useTexture) {
        baseColor = texture(texture_diffuse1, TexCoords).rgb;
    } else {
        baseColor = objectColor;
    }
    
    // === Calcul de l'éclairage Lambert ===
    
    // Normalisation des vecteurs
    vec3 norm = normalize(Normal);
    vec3 lightDir = normalize(finalLightPos - FragPos);
    
    // 1. Composante ambiante
    vec3 ambient = ambientStrength * ambientColor;
    
    // 2. Composante diffuse (Lambert)
    // Le modèle de Lambert utilise le produit scalaire entre la normale et la direction de la lumière
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * finalLightColor * lightIntensity;
    
    // === Calcul de l'atténuation de la distance ===
    float distance = length(finalLightPos - FragPos);
    float attenuation = 1.0 / (1.0 + 0.09 * distance + 0.032 * distance * distance);
    
    // Application de l'atténuation aux composantes
    diffuse *= attenuation;
    
    // === Assemblage final ===
    vec3 result = (ambient + diffuse) * baseColor;
    
    // Gamma correction (optionnel)
    result = pow(result, vec3(1.0/2.2));
    
    FragColor = vec4(result, 1.0);
}
