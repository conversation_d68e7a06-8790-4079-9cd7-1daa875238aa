#version 330 core

// === Sortie ===
out vec4 FragColor;

// === Entrée du vertex shader ===
in vec2 TexCoords;

// === Uniforms ===
uniform sampler2D scene;
uniform sampler2D bloomBlur;
uniform float exposure;
uniform float bloomStrength;

void main() {
    const float gamma = 2.2;
    
    vec3 hdrColor = texture(scene, TexCoords).rgb;
    vec3 bloomColor = texture(bloomBlur, TexCoords).rgb;
    
    // Additive blending pour le bloom
    hdrColor += bloomColor * bloomStrength;
    
    // Tone mapping (Reinhard)
    vec3 result = hdrColor / (hdrColor + vec3(1.0));
    
    // Gamma correction
    result = pow(result, vec3(1.0 / gamma));
    
    FragColor = vec4(result, 1.0);
}
