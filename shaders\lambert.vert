#version 330 core

// === Attributs d'entrée ===
layout (location = 0) in vec3 aPos;      // Position du vertex
layout (location = 1) in vec3 aNormal;   // Normale du vertex
layout (location = 2) in vec2 aTexCoord; // Coordonnées de texture (optionnel)

// === Uniforms traditionnels (compatibilité) ===
uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;

// === UBO pour caméra et projection ===
layout (std140) uniform CameraData {
    mat4 uView;
    mat4 uProjection;
    vec3 uViewPos;
    float padding1;
    vec3 uViewDir;
    float padding2;
};

// === UBO pour transformation ===
layout (std140) uniform TransformData {
    mat4 uModel;
    mat4 uNormalMatrix;
};

// === Sorties vers le fragment shader ===
out vec3 FragPos;       // Position du fragment dans l'espace monde
out vec3 Normal;        // Normale interpolée
out vec2 TexCoords;     // Coordonnées de texture
out vec3 ViewPos;       // Position de la caméra

void main() {
    // Utilise les UBO si disponibles, sinon les uniforms traditionnels
    mat4 finalModel = (uModel[0][0] != 0.0 || uModel[0][1] != 0.0 || uModel[0][2] != 0.0 || uModel[0][3] != 0.0 ||
                       uModel[1][0] != 0.0 || uModel[1][1] != 0.0 || uModel[1][2] != 0.0 || uModel[1][3] != 0.0 ||
                       uModel[2][0] != 0.0 || uModel[2][1] != 0.0 || uModel[2][2] != 0.0 || uModel[2][3] != 0.0 ||
                       uModel[3][0] != 0.0 || uModel[3][1] != 0.0 || uModel[3][2] != 0.0 || uModel[3][3] != 0.0) ? uModel : model;
    
    mat4 finalView = (uView[0][0] != 0.0 || uView[0][1] != 0.0 || uView[0][2] != 0.0 || uView[0][3] != 0.0 ||
                      uView[1][0] != 0.0 || uView[1][1] != 0.0 || uView[1][2] != 0.0 || uView[1][3] != 0.0 ||
                      uView[2][0] != 0.0 || uView[2][1] != 0.0 || uView[2][2] != 0.0 || uView[2][3] != 0.0 ||
                      uView[3][0] != 0.0 || uView[3][1] != 0.0 || uView[3][2] != 0.0 || uView[3][3] != 0.0) ? uView : view;
    
    mat4 finalProjection = (uProjection[0][0] != 0.0 || uProjection[0][1] != 0.0 || uProjection[0][2] != 0.0 || uProjection[0][3] != 0.0 ||
                           uProjection[1][0] != 0.0 || uProjection[1][1] != 0.0 || uProjection[1][2] != 0.0 || uProjection[1][3] != 0.0 ||
                           uProjection[2][0] != 0.0 || uProjection[2][1] != 0.0 || uProjection[2][2] != 0.0 || uProjection[2][3] != 0.0 ||
                           uProjection[3][0] != 0.0 || uProjection[3][1] != 0.0 || uProjection[3][2] != 0.0 || uProjection[3][3] != 0.0) ? uProjection : projection;
    
    // Calcul de la position du fragment dans l'espace monde
    FragPos = vec3(finalModel * vec4(aPos, 1.0));
    
    // Transformation de la normale
    // Utilise la matrice normale si disponible dans l'UBO, sinon calcule
    mat3 normalMat;
    if (uNormalMatrix[0][0] != 0.0 || uNormalMatrix[0][1] != 0.0 || uNormalMatrix[0][2] != 0.0 ||
        uNormalMatrix[1][0] != 0.0 || uNormalMatrix[1][1] != 0.0 || uNormalMatrix[1][2] != 0.0 ||
        uNormalMatrix[2][0] != 0.0 || uNormalMatrix[2][1] != 0.0 || uNormalMatrix[2][2] != 0.0) {
        normalMat = mat3(uNormalMatrix);
    } else {
        normalMat = mat3(transpose(inverse(finalModel)));
    }
    Normal = normalize(normalMat * aNormal);
    
    // Coordonnées de texture
    TexCoords = aTexCoord;
    
    // Position de la caméra (utilise UBO si disponible)
    ViewPos = (length(uViewPos) > 0.0) ? uViewPos : vec3(inverse(finalView)[3]);
    
    // Position finale du vertex
    gl_Position = finalProjection * finalView * vec4(FragPos, 1.0);
}
