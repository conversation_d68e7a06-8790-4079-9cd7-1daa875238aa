# Projet OpenGL – ESIEE IT Computer Graphics

🔮 Projet de Computer Graphics @ESIEE Paris

## Objectif
Créer une scène 3D avancée en utilisant OpenGL 3.3+ avec plusieurs objets, systèmes d'éclairage sophistiqués, environment mapping, et post-traitement en temps réel.

## ✅ Fonctionnalités Implémentées

### 🎨 Système de Rendu Avancé
- **Shaders multiples** avec choix en temps réel :
  - **Lambert** : Éclairage diffus optimisé
  - **Phong** : Éclairage complet (ambiant + diffus + spéculaire)
  - **Environment Mapping** : Reflets de l'environnement avec skybox
- **Skybox** : Environnement 3D immersif avec textures cubemap
- **Chargement OBJ** : Support complet avec matériaux via TinyOBJLoader
- **Gestion sRGB** : Rendu couleur correct

### 🚀 Optimisations Graphiques
- **UBO (Uniform Buffer Objects)** : Optimisation des performances
  - UBO Caméra/Projection (binding point 0)
  - UBO Transformations (binding point 1)
  - UBO Éclairage (binding point 2)
- **Classe Mat4 personnalisée** : Opérations matricielles optimisées
- **Architecture modulaire** : Code propre et extensible

### 🎭 Post-Traitement en Temps Réel
- **Framebuffer Objects (FBO)** : Pipeline de post-traitement
- **Effets disponibles** :
  - **Bloom** : Effet de halo lumineux
  - **Blur** : Flou gaussien (horizontal/vertical)
  - **Extensible** : Architecture pour ajouter d'autres effets
- **HDR** : Rendu haute gamme dynamique

### 🎮 Interface Utilisateur Interactive
- **ImGui** : Interface graphique complète
- **Contrôles en temps réel** :
  - Basculer entre modes de shading
  - Activer/désactiver post-traitement
  - Régler paramètres d'éclairage
  - Contrôles audio
- **Visualisation clavier** : Interface AZERTY avec feedback visuel

### 🌟 Scènes Multiples
- **MainScene** : Scène spatiale complète (vaisseau, planètes, astéroïdes)
- **LightScene** : Démonstration d'éclairage avec contrôles interactifs
- **Basculement** : Touche 'P' pour changer de scène

### 🔊 Système Audio
- **OpenAL** : Audio 3D spatialisé
- **Contrôles** : Volume, pitch, lecture/pause
- **Format** : Support WAV

## 📚 Technologies et Librairies

### Librairies Principales
- **[OpenGL 3.3+](https://www.opengl.org/)** — API graphique moderne
- **[GLFW](https://www.glfw.org/)** — Gestion fenêtre et contexte
- **[GLEW](http://glew.sourceforge.net/)** — Extensions OpenGL
- **[GLM](https://github.com/g-truc/glm)** — Mathématiques 3D
- **[TinyOBJLoader](https://github.com/tinyobjloader/tinyobjloader)** — Chargement modèles 3D
- **[ImGui](https://github.com/ocornut/imgui)** — Interface graphique
- **[stb_image](https://github.com/nothings/stb)** — Chargement textures
- **[OpenAL](https://www.openal.org/)** — Audio 3D (optionnel)

### Prérequis Système
- **OS** : Windows (MSYS2), Linux, macOS
- **OpenGL** : 3.3+ avec support shaders et framebuffers
- **Compilateur** : C++17 compatible (GCC, Clang, MSVC)
- **RAM** : 512 MB minimum, 2 GB recommandé
- **GPU** : Support OpenGL 3.3+ avec 512 MB VRAM

## 🛠️ Installation et Compilation

### MSYS2 (Windows) - Recommandé
```bash
# 1. Installer les dépendances
pacman -S mingw-w64-x86_64-cmake mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-glfw mingw-w64-x86_64-glew
pacman -S mingw-w64-x86_64-glm mingw-w64-x86_64-openal

# 2. Cloner et compiler
git clone https://github.com/Berachem/Computer_Graphics.git
cd Computer_Graphics
mkdir build && cd build
cmake ..
make -j4

# 3. Exécuter
./ProjetOpenGL.exe
```

### Linux (Ubuntu/Debian)
```bash
# 1. Installer les dépendances
sudo apt update
sudo apt install build-essential cmake
sudo apt install libglfw3-dev libglew-dev libglm-dev
sudo apt install libopenal-dev

# 2. Compiler et exécuter
git clone https://github.com/Berachem/Computer_Graphics.git
cd Computer_Graphics
mkdir build && cd build
cmake ..
make -j4
./ProjetOpenGL
```

## 📁 Structure du Projet

```
Computer_Graphics/
├── 📂 src/                     # Code source C++
│   ├── main.cpp               # Point d'entrée principal
│   ├── MainScene.cpp          # Scène spatiale principale
│   ├── LightScene.cpp         # Scène de démonstration d'éclairage
│   ├── Mat4.cpp              # Classe matrice 4x4 personnalisée
│   ├── UBO.cpp               # Uniform Buffer Objects
│   ├── Skybox.cpp            # Système de skybox
│   ├── PostProcessor.cpp     # Post-traitement
│   └── ...                   # Autres classes (Camera, Shader, etc.)
├── 📂 include/                # Headers C++
│   ├── Mat4.h, UBO.h         # Nouvelles fonctionnalités
│   ├── Skybox.h, PostProcessor.h
│   └── ...                   # Autres headers
├── 📂 shaders/               # Shaders GLSL
│   ├── lambert.vert/.frag    # Shading Lambert
│   ├── envmap.vert/.frag     # Environment mapping
│   ├── skybox.vert/.frag     # Rendu skybox
│   ├── screen.vert           # Post-traitement
│   ├── blur.frag, bloom.frag # Effets visuels
│   └── ...                   # Autres shaders
├── 📂 skybox/skybox_colorer/ # Textures cubemap
│   ├── right.png, left.png   # Faces de la skybox
│   ├── top.png, bottom.png
│   └── front.png, back.png
├── 📂 models/                # Modèles 3D (.obj)
├── 📂 sound/                 # Fichiers audio (.wav)
├── 📂 extern/imgui/          # Librairie ImGui
├── 📂 libs/                  # Headers externes (stb_image, etc.)
├── CMakeLists.txt            # Configuration build
├── README.md                 # Ce fichier
├── GUIDE_UTILISATION.md      # Guide utilisateur
├── NOUVELLES_FONCTIONNALITES.md # Documentation technique
└── TODO.md                   # État du projet
```

## 🎮 Utilisation Rapide

### Contrôles de Base
- **Navigation** : AZERTY (A=avant, Q=gauche, S=arrière, D=droite, A=haut, E=bas)
- **Souris** : Regarder autour (TAB pour basculer mode souris/interface)
- **P** : Changer de scène (MainScene ↔ LightScene)
- **O** : Arrêter tous les sons

### Fonctionnalités à Tester
1. **Modes de shading** : Interface "Options de Shading"
2. **Post-traitement** : Interface "Post-Traitement"
3. **Audio** : Interface "Contrôles Audio" (MainScene)
4. **Éclairage** : Interface "Contrôles de Lumière" (LightScene)

## 📖 Documentation

- **[GUIDE_UTILISATION.md](GUIDE_UTILISATION.md)** — Guide utilisateur complet
- **[NOUVELLES_FONCTIONNALITES.md](NOUVELLES_FONCTIONNALITES.md)** — Documentation technique
- **[TODO.md](TODO.md)** — État du projet et fonctionnalités

## 🔗 Liens Utiles

- **[LearnOpenGL](https://learnopengl.com/)** — Tutoriels OpenGL modernes
- **[OpenGL Reference](https://www.khronos.org/opengl/wiki/)** — Documentation officielle
- **[ImGui Demo](https://github.com/ocornut/imgui#demo)** — Exemples d'interface
- **[GLM Documentation](https://glm.g-truc.net/)** — Mathématiques 3D

## 👥 Contact

**Développeur** : [<EMAIL>](mailto:<EMAIL>)
**Projet** : Computer Graphics @ESIEE Paris
**Version** : 2.0 avec fonctionnalités avancées

---

*Projet réalisé dans le cadre du cours Computer Graphics - ESIEE Paris*

