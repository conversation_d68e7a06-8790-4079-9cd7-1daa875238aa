#include "UBO.h"
#include <iostream>
#include <glm/gtc/matrix_transform.hpp>

// === Implémentation de la classe UBO ===

UBO::UBO() 
    : bufferID(0), bindingPoint(0), bufferSize(0), isInitialized(false) {
}

UBO::~UBO() {
    Cleanup();
}

UBO::UBO(UBO&& other) noexcept 
    : bufferID(other.bufferID), bindingPoint(other.bindingPoint), 
      bufferSize(other.bufferSize), isInitialized(other.isInitialized) {
    // Réinitialise l'objet source
    other.bufferID = 0;
    other.bindingPoint = 0;
    other.bufferSize = 0;
    other.isInitialized = false;
}

UBO& UBO::operator=(UBO&& other) noexcept {
    if (this != &other) {
        // Nettoie les ressources actuelles
        Cleanup();
        
        // Transfère les ressources
        bufferID = other.bufferID;
        bindingPoint = other.bindingPoint;
        bufferSize = other.bufferSize;
        isInitialized = other.isInitialized;
        
        // Réinitialise l'objet source
        other.bufferID = 0;
        other.bindingPoint = 0;
        other.bufferSize = 0;
        other.isInitialized = false;
    }
    return *this;
}

bool UBO::Initialize(size_t size, GLuint binding, GLenum usage) {
    if (isInitialized) {
        std::cout << "Attention: UBO déjà initialisé" << std::endl;
        return false;
    }
    
    // Génère le buffer
    glGenBuffers(1, &bufferID);
    if (bufferID == 0) {
        std::cout << "Erreur: Impossible de créer le buffer UBO" << std::endl;
        return false;
    }
    
    // Lie et alloue la mémoire
    glBindBuffer(GL_UNIFORM_BUFFER, bufferID);
    glBufferData(GL_UNIFORM_BUFFER, size, nullptr, usage);
    
    // Vérifie les erreurs OpenGL
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        std::cout << "Erreur OpenGL lors de l'initialisation UBO: " << error << std::endl;
        glDeleteBuffers(1, &bufferID);
        bufferID = 0;
        return false;
    }
    
    // Lie le buffer au point de liaison
    glBindBufferBase(GL_UNIFORM_BUFFER, binding, bufferID);
    
    // Délie le buffer
    glBindBuffer(GL_UNIFORM_BUFFER, 0);
    
    // Sauvegarde les paramètres
    bufferSize = size;
    bindingPoint = binding;
    isInitialized = true;
    
    std::cout << "UBO initialisé: ID=" << bufferID << ", Binding=" << binding 
              << ", Size=" << size << " octets" << std::endl;
    
    return true;
}

void UBO::UpdateData(const void* data, size_t size, size_t offset) {
    if (!isInitialized) {
        std::cout << "Erreur: UBO non initialisé" << std::endl;
        return;
    }
    
    if (offset + size > bufferSize) {
        std::cout << "Erreur: Tentative d'écriture hors limites du buffer UBO" << std::endl;
        return;
    }
    
    // Lie le buffer et met à jour les données
    glBindBuffer(GL_UNIFORM_BUFFER, bufferID);
    glBufferSubData(GL_UNIFORM_BUFFER, offset, size, data);
    glBindBuffer(GL_UNIFORM_BUFFER, 0);
    
    // Vérifie les erreurs
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        std::cout << "Erreur OpenGL lors de la mise à jour UBO: " << error << std::endl;
    }
}

void UBO::Bind() const {
    if (isInitialized) {
        glBindBufferBase(GL_UNIFORM_BUFFER, bindingPoint, bufferID);
    }
}

void UBO::Unbind() const {
    glBindBufferBase(GL_UNIFORM_BUFFER, bindingPoint, 0);
}

void UBO::BindToShader(GLuint programID, const std::string& uniformBlockName) const {
    if (!isInitialized) {
        std::cout << "Erreur: UBO non initialisé" << std::endl;
        return;
    }
    
    // Obtient l'index du bloc uniform
    GLuint blockIndex = glGetUniformBlockIndex(programID, uniformBlockName.c_str());
    if (blockIndex == GL_INVALID_INDEX) {
        std::cout << "Attention: Bloc uniform '" << uniformBlockName 
                  << "' non trouvé dans le shader" << std::endl;
        return;
    }
    
    // Lie le bloc uniform au point de liaison
    glUniformBlockBinding(programID, blockIndex, bindingPoint);
    
    // Vérifie les erreurs
    GLenum error = glGetError();
    if (error != GL_NO_ERROR) {
        std::cout << "Erreur OpenGL lors de la liaison UBO au shader: " << error << std::endl;
    }
}

void UBO::Cleanup() {
    if (bufferID != 0) {
        glDeleteBuffers(1, &bufferID);
        bufferID = 0;
    }
    isInitialized = false;
    bufferSize = 0;
    bindingPoint = 0;
}

// === Implémentation de UBOManager ===

// Définition des membres statiques
UBO UBOManager::cameraUBO;
UBO UBOManager::transformUBO;
UBO UBOManager::lightingUBO;
bool UBOManager::initialized = false;

bool UBOManager::Initialize() {
    if (initialized) {
        std::cout << "UBOManager déjà initialisé" << std::endl;
        return true;
    }
    
    std::cout << "Initialisation du UBOManager..." << std::endl;
    
    // Initialise l'UBO de caméra
    if (!cameraUBO.Initialize(sizeof(CameraUBOData), CAMERA_BINDING_POINT)) {
        std::cout << "Erreur: Impossible d'initialiser l'UBO de caméra" << std::endl;
        return false;
    }
    
    // Initialise l'UBO de transformation
    if (!transformUBO.Initialize(sizeof(TransformUBOData), TRANSFORM_BINDING_POINT)) {
        std::cout << "Erreur: Impossible d'initialiser l'UBO de transformation" << std::endl;
        return false;
    }
    
    // Initialise l'UBO d'éclairage
    if (!lightingUBO.Initialize(sizeof(LightingUBOData), LIGHTING_BINDING_POINT)) {
        std::cout << "Erreur: Impossible d'initialiser l'UBO d'éclairage" << std::endl;
        return false;
    }
    
    initialized = true;
    std::cout << "UBOManager initialisé avec succès" << std::endl;
    return true;
}

void UBOManager::Cleanup() {
    cameraUBO = UBO();      // Utilise l'opérateur de déplacement
    transformUBO = UBO();
    lightingUBO = UBO();
    initialized = false;
    std::cout << "UBOManager nettoyé" << std::endl;
}

void UBOManager::UpdateCameraData(const CameraUBOData& data) {
    if (!initialized) {
        std::cout << "Erreur: UBOManager non initialisé" << std::endl;
        return;
    }
    cameraUBO.UpdateData(&data, sizeof(CameraUBOData));
}

void UBOManager::UpdateTransformData(const TransformUBOData& data) {
    if (!initialized) {
        std::cout << "Erreur: UBOManager non initialisé" << std::endl;
        return;
    }
    transformUBO.UpdateData(&data, sizeof(TransformUBOData));
}

void UBOManager::UpdateLightingData(const LightingUBOData& data) {
    if (!initialized) {
        std::cout << "Erreur: UBOManager non initialisé" << std::endl;
        return;
    }
    lightingUBO.UpdateData(&data, sizeof(LightingUBOData));
}

void UBOManager::BindToShader(GLuint programID) {
    if (!initialized) {
        std::cout << "Erreur: UBOManager non initialisé" << std::endl;
        return;
    }
    
    // Lie chaque UBO au shader avec les noms de blocs correspondants
    cameraUBO.BindToShader(programID, "CameraData");
    transformUBO.BindToShader(programID, "TransformData");
    lightingUBO.BindToShader(programID, "LightingData");
}
