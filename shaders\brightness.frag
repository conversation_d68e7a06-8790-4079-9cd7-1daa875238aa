#version 330 core

// === Sortie ===
out vec4 FragColor;

// === Entrée du vertex shader ===
in vec2 TexCoords;

// === Uniforms ===
uniform sampler2D scene;
uniform float threshold;

void main() {
    vec3 color = texture(scene, TexCoords).rgb;
    
    // Calcule la luminosité de la couleur
    float brightness = dot(color, vec3(0.2126, 0.7152, 0.0722));
    
    // Ne garde que les pixels au-dessus du seuil
    if (brightness > threshold) {
        FragColor = vec4(color, 1.0);
    } else {
        FragColor = vec4(0.0, 0.0, 0.0, 1.0);
    }
}
