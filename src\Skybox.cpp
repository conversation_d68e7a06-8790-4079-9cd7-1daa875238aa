#include "Skybox.h"
#include "Shader.h"
#include <iostream>
#include <glm/gtc/matrix_transform.hpp>

// stb_image est déjà implémenté dans TextureLoader.cpp
#include "../libs/stb_image.h"

// Définition des vertices de la skybox (cube centré à l'origine)
const float Skybox::skyboxVertices[108] = {
    // Positions          
    -1.0f,  1.0f, -1.0f,
    -1.0f, -1.0f, -1.0f,
     1.0f, -1.0f, -1.0f,
     1.0f, -1.0f, -1.0f,
     1.0f,  1.0f, -1.0f,
    -1.0f,  1.0f, -1.0f,

    -1.0f, -1.0f,  1.0f,
    -1.0f, -1.0f, -1.0f,
    -1.0f,  1.0f, -1.0f,
    -1.0f,  1.0f, -1.0f,
    -1.0f,  1.0f,  1.0f,
    -1.0f, -1.0f,  1.0f,

     1.0f, -1.0f, -1.0f,
     1.0f, -1.0f,  1.0f,
     1.0f,  1.0f,  1.0f,
     1.0f,  1.0f,  1.0f,
     1.0f,  1.0f, -1.0f,
     1.0f, -1.0f, -1.0f,

    -1.0f, -1.0f,  1.0f,
    -1.0f,  1.0f,  1.0f,
     1.0f,  1.0f,  1.0f,
     1.0f,  1.0f,  1.0f,
     1.0f, -1.0f,  1.0f,
    -1.0f, -1.0f,  1.0f,

    -1.0f,  1.0f, -1.0f,
     1.0f,  1.0f, -1.0f,
     1.0f,  1.0f,  1.0f,
     1.0f,  1.0f,  1.0f,
    -1.0f,  1.0f,  1.0f,
    -1.0f,  1.0f, -1.0f,

    -1.0f, -1.0f, -1.0f,
    -1.0f, -1.0f,  1.0f,
     1.0f, -1.0f, -1.0f,
     1.0f, -1.0f, -1.0f,
    -1.0f, -1.0f,  1.0f,
     1.0f, -1.0f,  1.0f
};

Skybox::Skybox() 
    : VAO(0), VBO(0), cubemapTexture(0), isInitialized(false) {
}

Skybox::~Skybox() {
    Cleanup();
}

Skybox::Skybox(Skybox&& other) noexcept 
    : VAO(other.VAO), VBO(other.VBO), cubemapTexture(other.cubemapTexture),
      skyboxShader(std::move(other.skyboxShader)), isInitialized(other.isInitialized) {
    // Réinitialise l'objet source
    other.VAO = 0;
    other.VBO = 0;
    other.cubemapTexture = 0;
    other.isInitialized = false;
}

Skybox& Skybox::operator=(Skybox&& other) noexcept {
    if (this != &other) {
        // Nettoie les ressources actuelles
        Cleanup();
        
        // Transfère les ressources
        VAO = other.VAO;
        VBO = other.VBO;
        cubemapTexture = other.cubemapTexture;
        skyboxShader = std::move(other.skyboxShader);
        isInitialized = other.isInitialized;
        
        // Réinitialise l'objet source
        other.VAO = 0;
        other.VBO = 0;
        other.cubemapTexture = 0;
        other.isInitialized = false;
    }
    return *this;
}

bool Skybox::Initialize(const std::vector<std::string>& faces) {
    if (isInitialized) {
        std::cout << "Attention: Skybox déjà initialisée" << std::endl;
        return false;
    }
    
    if (faces.size() != 6) {
        std::cout << "Erreur: Il faut exactement 6 faces pour la cubemap" << std::endl;
        return false;
    }
    
    std::cout << "Initialisation de la skybox..." << std::endl;
    
    // Charge la texture cubemap
    cubemapTexture = LoadCubemap(faces);
    if (cubemapTexture == 0) {
        std::cout << "Erreur: Impossible de charger la texture cubemap" << std::endl;
        return false;
    }
    
    // Configure la géométrie
    SetupSkyboxGeometry();
    
    // Charge les shaders
    try {
        skyboxShader = std::make_unique<Shader>("shaders/skybox.vert", "shaders/skybox.frag");
    } catch (const std::exception& e) {
        std::cout << "Erreur lors du chargement des shaders skybox: " << e.what() << std::endl;
        Cleanup();
        return false;
    }
    
    isInitialized = true;
    std::cout << "Skybox initialisée avec succès" << std::endl;
    return true;
}

bool Skybox::InitializeDefault() {
    // Utilise les textures du dossier skybox_colorer
    std::vector<std::string> faces = {
        "../skybox/skybox_colorer/right.png",   // +X
        "../skybox/skybox_colorer/left.png",    // -X
        "../skybox/skybox_colorer/top.png",     // +Y
        "../skybox/skybox_colorer/bottom.png",  // -Y
        "../skybox/skybox_colorer/front.png",   // +Z
        "../skybox/skybox_colorer/back.png"     // -Z
    };
    
    return Initialize(faces);
}

void Skybox::Render(const glm::mat4& view, const glm::mat4& projection) {
    if (!isInitialized) {
        return;
    }
    
    // Désactive l'écriture dans le depth buffer
    glDepthMask(GL_FALSE);
    
    // Active le shader
    skyboxShader->use();
    
    // Retire la translation de la matrice de vue
    glm::mat4 viewNoTranslation = glm::mat4(glm::mat3(view));
    
    // Passe les matrices au shader
    skyboxShader->setMat4("view", viewNoTranslation);
    skyboxShader->setMat4("projection", projection);
    
    // Lie la texture cubemap
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_CUBE_MAP, cubemapTexture);
    skyboxShader->setInt("skybox", 0);
    
    // Rend le cube
    glBindVertexArray(VAO);
    glDrawArrays(GL_TRIANGLES, 0, 36);
    glBindVertexArray(0);
    
    // Réactive l'écriture dans le depth buffer
    glDepthMask(GL_TRUE);
}

void Skybox::BindCubemap(int textureUnit) const {
    if (isInitialized) {
        glActiveTexture(GL_TEXTURE0 + textureUnit);
        glBindTexture(GL_TEXTURE_CUBE_MAP, cubemapTexture);
    }
}

void Skybox::UnbindCubemap() const {
    glBindTexture(GL_TEXTURE_CUBE_MAP, 0);
}

void Skybox::SetupSkyboxGeometry() {
    // Génère et lie le VAO
    glGenVertexArrays(1, &VAO);
    glGenBuffers(1, &VBO);
    
    glBindVertexArray(VAO);
    
    // Charge les données dans le VBO
    glBindBuffer(GL_ARRAY_BUFFER, VBO);
    glBufferData(GL_ARRAY_BUFFER, sizeof(skyboxVertices), skyboxVertices, GL_STATIC_DRAW);
    
    // Configure les attributs de vertex (positions uniquement)
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 3 * sizeof(float), (void*)0);
    
    // Délie
    glBindBuffer(GL_ARRAY_BUFFER, 0);
    glBindVertexArray(0);
}

void Skybox::Cleanup() {
    if (VAO != 0) {
        glDeleteVertexArrays(1, &VAO);
        VAO = 0;
    }
    if (VBO != 0) {
        glDeleteBuffers(1, &VBO);
        VBO = 0;
    }
    if (cubemapTexture != 0) {
        glDeleteTextures(1, &cubemapTexture);
        cubemapTexture = 0;
    }
    skyboxShader.reset();
    isInitialized = false;
}

// === Implémentation de EnvironmentMapper ===

void EnvironmentMapper::ApplyToShader(Shader& shader, GLuint cubemapID, 
                                     float reflectivity, int textureUnit) {
    shader.use();
    
    // Active la texture cubemap
    glActiveTexture(GL_TEXTURE0 + textureUnit);
    glBindTexture(GL_TEXTURE_CUBE_MAP, cubemapID);
    
    // Configure les uniforms
    shader.setInt("environmentMap", textureUnit);
    shader.setFloat("reflectivity", reflectivity);
    shader.setBool("useEnvironmentMapping", true);
}

glm::vec3 EnvironmentMapper::CalculateReflection(const glm::vec3& viewDir, const glm::vec3& normal) {
    return reflect(viewDir, normal);
}

GLuint Skybox::LoadCubemap(const std::vector<std::string>& faces) {
    GLuint textureID;
    glGenTextures(1, &textureID);
    glBindTexture(GL_TEXTURE_CUBE_MAP, textureID);

    // Charge chaque face de la cubemap
    for (unsigned int i = 0; i < faces.size(); i++) {
        // Utilise stb_image pour charger l'image
        int width, height, nrChannels;
        unsigned char* data = stbi_load(faces[i].c_str(), &width, &height, &nrChannels, 0);
        if (data) {
            GLenum format = GL_RGB;
            if (nrChannels == 1)
                format = GL_RED;
            else if (nrChannels == 3)
                format = GL_RGB;
            else if (nrChannels == 4)
                format = GL_RGBA;

            glTexImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X + i,
                        0, format, width, height, 0, format, GL_UNSIGNED_BYTE, data);

            std::cout << "Face cubemap chargée: " << faces[i] << " (" << width << "x" << height << ")" << std::endl;
        } else {
            std::cout << "Erreur: Impossible de charger la texture cubemap: " << faces[i] << std::endl;
            stbi_image_free(data);
            glDeleteTextures(1, &textureID);
            return 0;
        }
        stbi_image_free(data);
    }

    // Configure les paramètres de texture
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_CUBE_MAP, GL_TEXTURE_WRAP_R, GL_CLAMP_TO_EDGE);

    glBindTexture(GL_TEXTURE_CUBE_MAP, 0);
    return textureID;
}
