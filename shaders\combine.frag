#version 330 core

// === Sortie ===
out vec4 FragColor;

// === Entrée du vertex shader ===
in vec2 TexCoords;

// === Uniforms ===
uniform sampler2D texture1;
uniform sampler2D texture2;
uniform float blend;

void main() {
    vec3 color1 = texture(texture1, TexCoords).rgb;
    vec3 color2 = texture(texture2, TexCoords).rgb;
    
    // Combine les deux textures avec le facteur de mélange
    vec3 result = mix(color1, color2, blend);
    
    FragColor = vec4(result, 1.0);
}
