#version 330 core

// === Attributs d'entrée ===
layout (location = 0) in vec3 aPos;

// === Uniforms ===
uniform mat4 view;
uniform mat4 projection;

// === Sortie vers le fragment shader ===
out vec3 TexCoords;

void main() {
    // Les coordonnées de texture de la skybox sont les positions du vertex
    TexCoords = aPos;
    
    // Calcul de la position finale
    // On utilise la position homogène pour s'assurer que la skybox
    // est toujours rendue à la distance maximale (z = w)
    vec4 pos = projection * view * vec4(aPos, 1.0);
    gl_Position = pos.xyww;
}
