#ifndef SKYBOX_H
#define SKYBOX_H

#include <GL/glew.h>
#include <glm/glm.hpp>
#include <string>
#include <vector>
#include <memory>

class Shader;

/**
 * @brief Classe Skybox - Gestion de l'environment mapping avec cubemap
 * 
 * Cette classe implémente un système de skybox utilisant une texture cubemap
 * pour créer un environnement 3D immersif. Elle supporte également l'environment
 * mapping pour les reflets sur les objets.
 */
class Skybox {
private:
    GLuint VAO, VBO;                    // Vertex Array Object et Vertex Buffer Object
    GLuint cubemapTexture;              // Texture cubemap
    std::unique_ptr<Shader> skyboxShader; // Shader pour le rendu de la skybox
    bool isInitialized;                 // État d'initialisation
    
    // Vertices du cube pour la skybox (positions uniquement)
    static const float skyboxVertices[108];

public:
    /**
     * @brief Constructeur par défaut
     */
    Skybox();
    
    /**
     * @brief Destructeur - Libère les ressources OpenGL
     */
    ~Skybox();
    
    /**
     * @brief Constructeur de copie supprimé (ressource unique)
     */
    Skybox(const Skybox&) = delete;
    
    /**
     * @brief Opérateur d'assignation supprimé (ressource unique)
     */
    Skybox& operator=(const Skybox&) = delete;
    
    /**
     * @brief Constructeur de déplacement
     */
    Skybox(Skybox&& other) noexcept;
    
    /**
     * @brief Opérateur d'assignation de déplacement
     */
    Skybox& operator=(Skybox&& other) noexcept;

    /**
     * @brief Initialise la skybox avec les textures spécifiées
     * @param faces Vecteur contenant les chemins vers les 6 faces de la cubemap
     *              dans l'ordre: right, left, top, bottom, front, back
     * @return true si l'initialisation réussit, false sinon
     */
    bool Initialize(const std::vector<std::string>& faces);
    
    /**
     * @brief Initialise la skybox avec les textures du dossier skybox_colorer
     * @return true si l'initialisation réussit, false sinon
     */
    bool InitializeDefault();
    
    /**
     * @brief Rend la skybox
     * @param view Matrice de vue (sans translation)
     * @param projection Matrice de projection
     */
    void Render(const glm::mat4& view, const glm::mat4& projection);
    
    /**
     * @brief Lie la texture cubemap pour l'environment mapping
     * @param textureUnit Unité de texture à utiliser (défaut: 0)
     */
    void BindCubemap(int textureUnit = 0) const;
    
    /**
     * @brief Délie la texture cubemap
     */
    void UnbindCubemap() const;
    
    /**
     * @brief Retourne l'ID de la texture cubemap
     * @return ID de la texture cubemap
     */
    GLuint GetCubemapID() const { return cubemapTexture; }
    
    /**
     * @brief Vérifie si la skybox est initialisée
     * @return true si initialisée, false sinon
     */
    bool IsInitialized() const { return isInitialized; }

private:
    /**
     * @brief Charge une texture cubemap à partir des fichiers spécifiés
     * @param faces Vecteur contenant les chemins vers les 6 faces
     * @return ID de la texture cubemap ou 0 en cas d'erreur
     */
    GLuint LoadCubemap(const std::vector<std::string>& faces);
    
    /**
     * @brief Initialise les buffers OpenGL pour la géométrie de la skybox
     */
    void SetupSkyboxGeometry();
    
    /**
     * @brief Libère les ressources OpenGL
     */
    void Cleanup();
};

/**
 * @brief Classe utilitaire pour l'environment mapping
 * 
 * Cette classe fournit des méthodes pour appliquer l'environment mapping
 * aux objets de la scène en utilisant la texture cubemap de la skybox.
 */
class EnvironmentMapper {
public:
    /**
     * @brief Applique l'environment mapping à un shader
     * @param shader Shader à configurer
     * @param cubemapID ID de la texture cubemap
     * @param reflectivity Facteur de réflectivité (0.0 à 1.0)
     * @param textureUnit Unité de texture pour la cubemap
     */
    static void ApplyToShader(Shader& shader, GLuint cubemapID, 
                             float reflectivity = 0.5f, int textureUnit = 1);
    
    /**
     * @brief Calcule le vecteur de réflexion pour l'environment mapping
     * @param viewDir Direction de vue
     * @param normal Normale de surface
     * @return Vecteur de réflexion
     */
    static glm::vec3 CalculateReflection(const glm::vec3& viewDir, const glm::vec3& normal);
};

#endif // SKYBOX_H
