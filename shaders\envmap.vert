#version 330 core

// === Attributs d'entrée ===
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoord;

// === Uniforms ===
uniform mat4 model;
uniform mat4 view;
uniform mat4 projection;

// === Sorties vers le fragment shader ===
out vec3 FragPos;       // Position du fragment dans l'espace monde
out vec3 Normal;        // Normale dans l'espace monde
out vec2 TexCoords;     // Coordonnées de texture
out vec3 ViewPos;       // Position de la caméra
out vec3 ReflectDir;    // Direction de réflexion pour environment mapping

void main() {
    // Position du fragment dans l'espace monde
    FragPos = vec3(model * vec4(aPos, 1.0));
    
    // Normale dans l'espace monde
    Normal = mat3(transpose(inverse(model))) * aNormal;
    
    // Coordonnées de texture
    TexCoords = aTexCoord;
    
    // Position de la caméra (extraite de la matrice de vue)
    ViewPos = vec3(inverse(view)[3]);
    
    // Calcul de la direction de réflexion pour environment mapping
    vec3 viewDir = normalize(FragPos - ViewPos);
    ReflectDir = reflect(viewDir, normalize(Normal));
    
    // Position finale du vertex
    gl_Position = projection * view * vec4(FragPos, 1.0);
}
