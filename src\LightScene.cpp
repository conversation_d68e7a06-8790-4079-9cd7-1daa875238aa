#include "LightScene.h"
#include "imgui.h"
#include <iostream>
#include <glm/gtc/matrix_transform.hpp>

LightScene::LightScene()
    : lightPosition(-50.0f, 20.0f, -50.0f),  // Position statique éloignée
      lightColor(1.0f, 1.0f, 0.8f),
      lightRadius(5.0f),                      // Plus grande pour être visible de loin
      rotationSpeed(0.0f),                    // Pas de rotation
      currentRotation(0.0f),
      initialized(false),
      usePostProcessing(false) {
}

LightScene::~LightScene() {
    Cleanup();
}

bool LightScene::Initialize(Camera& camera, SoundManager& soundManager) {
    if (initialized) {
        return true;
    }

    std::cout << "Initialisation de la LightScene..." << std::endl;

    // Charger les shaders
    if (!LoadShaders()) {
        std::cerr << "Erreur : échec du chargement des shaders pour LightScene" << std::endl;
        return false;
    }

    // Créer la sphère de lumière
    if (!CreateLightSphere()) {
        std::cerr << "Erreur : échec de la création de la sphère de lumière" << std::endl;
        return false;
    }

    initialized = true;
    std::cout << "LightScene initialisée avec succès" << std::endl;
    return true;
}

bool LightScene::LoadShaders() {
    try {
        // Utiliser le shader du soleil pour la source de lumière
        lightShader = std::make_unique<Shader>("../shaders/sun.vert", "../shaders/sun.frag");
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Erreur lors du chargement des shaders LightScene : " << e.what() << std::endl;
        return false;
    }
}

bool LightScene::CreateLightSphere() {
    try {
        // Créer une sphère simple sans texture pour représenter la lumière
        lightSphere = std::make_unique<Sphere>("", lightRadius, 32, 16);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Erreur lors de la création de la sphère de lumière : " << e.what() << std::endl;
        return false;
    }
}

bool LightScene::InitializeNewFeatures(int screenWidth, int screenHeight) {
    std::cout << "Initialisation des nouvelles fonctionnalités pour LightScene..." << std::endl;

    // Initialise le manager UBO (partagé entre les scènes)
    if (!UBOManager::IsInitialized()) {
        if (!UBOManager::Initialize()) {
            std::cout << "Erreur: Impossible d'initialiser le UBOManager" << std::endl;
            return false;
        }
    }

    // Initialise la skybox (même que MainScene)
    skybox = std::make_unique<Skybox>();
    if (!skybox->InitializeDefault()) {
        std::cout << "Erreur: Impossible d'initialiser la skybox pour LightScene" << std::endl;
        return false;
    }

    // Initialise le post-processor
    postProcessor = std::make_unique<PostProcessor>();
    if (!postProcessor->Initialize(screenWidth, screenHeight)) {
        std::cout << "Erreur: Impossible d'initialiser le post-processor pour LightScene" << std::endl;
        return false;
    }

    std::cout << "Nouvelles fonctionnalités initialisées avec succès pour LightScene" << std::endl;
    return true;
}

void LightScene::Update(float deltaTime, GLFWwindow* window, Camera& camera, SoundManager& soundManager) {
    if (!initialized) return;

    // Lumière statique - pas de mise à jour de position nécessaire
    // La position reste fixe pour permettre l'ajout d'objets plus tard
}



void LightScene::Render(Camera& camera, int screenWidth, int screenHeight) {
    if (!initialized) return;

    // Initialise les nouvelles fonctionnalités si pas encore fait
    static bool newFeaturesInitialized = false;
    if (!newFeaturesInitialized) {
        if (InitializeNewFeatures(screenWidth, screenHeight)) {
            newFeaturesInitialized = true;
        } else {
            // Fallback vers le rendu classique
            RenderLight(camera, screenWidth, screenHeight);
            return;
        }
    }

    // Met à jour les UBO avec les données de caméra
    if (UBOManager::IsInitialized()) {
        CameraUBOData cameraData;
        cameraData.view = camera.GetViewMatrix();
        cameraData.projection = glm::perspective(glm::radians(camera.Zoom),
                                                (float)screenWidth / (float)screenHeight,
                                                0.1f, 100.0f);
        cameraData.viewPos = camera.Position;
        cameraData.viewDir = camera.Front;
        UBOManager::UpdateCameraData(cameraData);

        // Met à jour les données d'éclairage
        LightingUBOData lightingData;
        lightingData.lightPos = lightPosition;
        lightingData.lightColor = lightColor;
        lightingData.lightIntensity = 1.0f;
        lightingData.ambientColor = glm::vec3(0.1f, 0.1f, 0.15f);
        lightingData.ambientStrength = 0.1f;
        UBOManager::UpdateLightingData(lightingData);
    }

    // Rendu avec ou sans post-traitement
    if (usePostProcessing && postProcessor && postProcessor->IsInitialized()) {
        // Commence le rendu vers le framebuffer
        postProcessor->BeginSceneRender();

        // Rend la skybox en premier
        if (skybox && skybox->IsInitialized()) {
            glm::mat4 view = camera.GetViewMatrix();
            glm::mat4 projection = glm::perspective(glm::radians(camera.Zoom),
                                                  (float)screenWidth / (float)screenHeight,
                                                  0.1f, 100.0f);
            skybox->Render(view, projection);
        }

        // Rend la lumière
        RenderLight(camera, screenWidth, screenHeight);

        // Applique les effets et rend à l'écran
        std::vector<PostProcessEffect> effects = { PostProcessEffect::BLOOM };
        postProcessor->EndSceneRender(effects);
    } else {
        // Rendu classique
        // Rend la skybox en premier
        if (skybox && skybox->IsInitialized()) {
            glm::mat4 view = camera.GetViewMatrix();
            glm::mat4 projection = glm::perspective(glm::radians(camera.Zoom),
                                                  (float)screenWidth / (float)screenHeight,
                                                  0.1f, 100.0f);
            skybox->Render(view, projection);
        }

        RenderLight(camera, screenWidth, screenHeight);
    }
}

void LightScene::RenderLight(Camera& camera, int screenWidth, int screenHeight) {
    // Matrices de projection et de vue
    glm::mat4 projection = glm::perspective(glm::radians(camera.Zoom), 
                                          (float)screenWidth / (float)screenHeight, 
                                          0.1f, 100.0f);
    glm::mat4 view = camera.GetViewMatrix();

    // Rendu de la sphère de lumière
    lightShader->use();
    lightShader->setMat4("projection", projection);
    lightShader->setMat4("view", view);
    lightShader->setFloat("time", static_cast<float>(glfwGetTime()));

    // Positionner la sphère de lumière
    glm::mat4 model = glm::mat4(1.0f);
    model = glm::translate(model, lightPosition);
    lightShader->setMat4("model", model);

    // Dessiner la sphère
    lightSphere->Draw(*lightShader);
}

void LightScene::RenderUI(GLFWwindow* window, SoundManager& soundManager) {
    if (!initialized) return;

    // Interface spécifique à la scène de lumière
    RenderLightSceneUI();
}



const char* LightScene::GetName() const {
    return "Scène de Lumière";
}

void LightScene::OnActivate() {
    std::cout << "LightScene activée - Scène d'éclairage statique prête pour l'ajout d'objets" << std::endl;
}

void LightScene::OnDeactivate() {
    std::cout << "LightScene désactivée" << std::endl;
}



void LightScene::RenderLightSceneUI() {
    ImGui::SetNextWindowPos(ImVec2(10, 10), ImGuiCond_FirstUseEver);
    ImGui::Begin("Contrôles Scène de Lumière", nullptr, ImGuiWindowFlags_AlwaysAutoResize);

    ImGui::Text("Scène de démonstration d'éclairage");
    ImGui::Separator();

    // Contrôles de la lumière
    ImGui::Text("Position de la lumière:");
    ImGui::SliderFloat("X##light", &lightPosition.x, -100.0f, 100.0f);
    ImGui::SliderFloat("Y##light", &lightPosition.y, 0.0f, 50.0f);
    ImGui::SliderFloat("Z##light", &lightPosition.z, -100.0f, 100.0f);

    ImGui::Text("Couleur de la lumière:");
    ImGui::ColorEdit3("Couleur##light", &lightColor.x);

    ImGui::Text("Rayon de la sphère:");
    if (ImGui::SliderFloat("Rayon##light", &lightRadius, 1.0f, 20.0f)) {
        // Recrée la sphère avec le nouveau rayon
        lightSphere = std::make_unique<Sphere>("", lightRadius, 32, 16);
    }

    ImGui::Separator();

    // Post-traitement
    ImGui::Checkbox("Post-traitement", &usePostProcessing);
    if (usePostProcessing) {
        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Bloom activé");
    }

    ImGui::Separator();
    ImGui::Text("Cette scène utilise:");
    ImGui::BulletText("Skybox avec environment mapping");
    ImGui::BulletText("UBO pour optimisation");
    ImGui::BulletText("Post-traitement (bloom)");

    ImGui::End();
}

void LightScene::Cleanup() {
    // Nettoie les nouvelles fonctionnalités
    skybox.reset();
    postProcessor.reset();
    // Note: UBOManager est partagé, ne pas le nettoyer ici

    lightShader.reset();
    lightSphere.reset();

    initialized = false;
    std::cout << "LightScene nettoyée" << std::endl;
}
