#version 330 core

// === Sortie ===
out vec4 FragColor;

// === Entrée du vertex shader ===
in vec2 TexCoords;

// === Uniforms ===
uniform sampler2D image;
uniform bool horizontal;
uniform float intensity;

// Poids pour le flou gaussien
uniform float weight[5] = float[] (0.227027, 0.1945946, 0.1216216, 0.054054, 0.016216);

void main() {
    vec2 tex_offset = 1.0 / textureSize(image, 0); // Taille d'un pixel
    vec3 result = texture(image, TexCoords).rgb * weight[0]; // Contribution du pixel central
    
    if (horizontal) {
        // Flou horizontal
        for (int i = 1; i < 5; ++i) {
            result += texture(image, TexCoords + vec2(tex_offset.x * i * intensity, 0.0)).rgb * weight[i];
            result += texture(image, TexCoords - vec2(tex_offset.x * i * intensity, 0.0)).rgb * weight[i];
        }
    } else {
        // Flou vertical
        for (int i = 1; i < 5; ++i) {
            result += texture(image, TexCoords + vec2(0.0, tex_offset.y * i * intensity)).rgb * weight[i];
            result += texture(image, TexCoords - vec2(0.0, tex_offset.y * i * intensity)).rgb * weight[i];
        }
    }
    
    FragColor = vec4(result, 1.0);
}
