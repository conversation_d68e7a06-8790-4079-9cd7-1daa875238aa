#include "PostProcessor.h"
#include "Shader.h"
#include <iostream>

// Vertices pour le quad plein écran (position + coordonnées de texture)
const float PostProcessor::quadVertices[24] = {
    // positions   // texCoords
    -1.0f,  1.0f,  0.0f, 1.0f,
    -1.0f, -1.0f,  0.0f, 0.0f,
     1.0f, -1.0f,  1.0f, 0.0f,

    -1.0f,  1.0f,  0.0f, 1.0f,
     1.0f, -1.0f,  1.0f, 0.0f,
     1.0f,  1.0f,  1.0f, 1.0f
};

PostProcessor::PostProcessor() 
    : sceneFBO(0), sceneColorTexture(0), sceneDepthTexture(0),
      brightnessFBO(0), brightnessTexture(0), quadVAO(0), quadVBO(0),
      screenWidth(0), screenHeight(0), isInitialized(false) {
    pingpongFBO[0] = pingpongFBO[1] = 0;
    pingpongTextures[0] = pingpongTextures[1] = 0;
}

PostProcessor::~PostProcessor() {
    Cleanup();
}

bool PostProcessor::Initialize(int width, int height) {
    if (isInitialized) {
        std::cout << "Attention: PostProcessor déjà initialisé" << std::endl;
        return false;
    }
    
    screenWidth = width;
    screenHeight = height;
    
    std::cout << "Initialisation du PostProcessor (" << width << "x" << height << ")..." << std::endl;
    
    // Crée les framebuffers
    if (!CreateFramebuffers()) {
        std::cout << "Erreur: Impossible de créer les framebuffers" << std::endl;
        return false;
    }
    
    // Configure la géométrie du quad
    SetupQuadGeometry();
    
    // Charge les shaders
    if (!LoadShaders()) {
        std::cout << "Erreur: Impossible de charger les shaders de post-traitement" << std::endl;
        Cleanup();
        return false;
    }
    
    isInitialized = true;
    std::cout << "PostProcessor initialisé avec succès" << std::endl;
    return true;
}

void PostProcessor::Resize(int width, int height) {
    if (!isInitialized) return;
    
    screenWidth = width;
    screenHeight = height;
    
    // Supprime les anciens framebuffers
    if (sceneFBO != 0) {
        glDeleteFramebuffers(1, &sceneFBO);
        glDeleteTextures(1, &sceneColorTexture);
        glDeleteTextures(1, &sceneDepthTexture);
    }
    
    if (pingpongFBO[0] != 0) {
        glDeleteFramebuffers(2, pingpongFBO);
        glDeleteTextures(2, pingpongTextures);
    }
    
    if (brightnessFBO != 0) {
        glDeleteFramebuffers(1, &brightnessFBO);
        glDeleteTextures(1, &brightnessTexture);
    }
    
    // Recrée les framebuffers avec les nouvelles dimensions
    CreateFramebuffers();
}

void PostProcessor::BeginSceneRender() {
    if (!isInitialized) return;
    
    // Lie le framebuffer de scène
    glBindFramebuffer(GL_FRAMEBUFFER, sceneFBO);
    glViewport(0, 0, screenWidth, screenHeight);
    
    // Efface le framebuffer
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
}

void PostProcessor::EndSceneRender(const std::vector<PostProcessEffect>& effects) {
    if (!isInitialized) return;
    
    // Revient au framebuffer par défaut
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    
    // Applique les effets demandés
    for (const auto& effect : effects) {
        ApplyEffect(effect);
    }
    
    // Rend le résultat final à l'écran
    RenderToScreen();
}

void PostProcessor::ApplyEffect(PostProcessEffect effect, float intensity) {
    if (!isInitialized) return;
    
    switch (effect) {
        case PostProcessEffect::BLUR:
            ApplyBlur(true, intensity);   // Flou horizontal
            ApplyBlur(false, intensity);  // Flou vertical
            break;
            
        case PostProcessEffect::BLOOM:
            ApplyBloom(intensity);
            break;
            
        case PostProcessEffect::EDGE_DETECTION:
        case PostProcessEffect::SHARPEN:
        case PostProcessEffect::GRAYSCALE:
        case PostProcessEffect::SEPIA:
        case PostProcessEffect::INVERT:
        case PostProcessEffect::VIGNETTE:
            // Ces effets seront implémentés dans les shaders
            break;
            
        case PostProcessEffect::NONE:
        default:
            break;
    }
}

void PostProcessor::RenderToScreen() {
    if (!isInitialized) return;
    
    // Désactive le test de profondeur pour le rendu plein écran
    glDisable(GL_DEPTH_TEST);
    
    // Utilise le shader d'écran
    screenShader->use();
    screenShader->setInt("screenTexture", 0);
    
    // Lie la texture de scène
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, sceneColorTexture);
    
    // Rend le quad plein écran
    glBindVertexArray(quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);
    
    // Réactive le test de profondeur
    glEnable(GL_DEPTH_TEST);
}

bool PostProcessor::CreateFramebuffers() {
    // === Framebuffer principal de scène ===
    glGenFramebuffers(1, &sceneFBO);
    glBindFramebuffer(GL_FRAMEBUFFER, sceneFBO);
    
    // Texture couleur
    glGenTextures(1, &sceneColorTexture);
    glBindTexture(GL_TEXTURE_2D, sceneColorTexture);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA16F, screenWidth, screenHeight, 0, GL_RGBA, GL_FLOAT, NULL);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, sceneColorTexture, 0);
    
    // Texture de profondeur
    glGenTextures(1, &sceneDepthTexture);
    glBindTexture(GL_TEXTURE_2D, sceneDepthTexture);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_DEPTH_COMPONENT24, screenWidth, screenHeight, 0, GL_DEPTH_COMPONENT, GL_FLOAT, NULL);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_DEPTH_ATTACHMENT, GL_TEXTURE_2D, sceneDepthTexture, 0);
    
    // Vérifie le framebuffer
    if (glCheckFramebufferStatus(GL_FRAMEBUFFER) != GL_FRAMEBUFFER_COMPLETE) {
        std::cout << "Erreur: Framebuffer de scène incomplet" << std::endl;
        return false;
    }
    
    // === Framebuffers ping-pong pour les effets ===
    glGenFramebuffers(2, pingpongFBO);
    glGenTextures(2, pingpongTextures);
    
    for (int i = 0; i < 2; i++) {
        glBindFramebuffer(GL_FRAMEBUFFER, pingpongFBO[i]);
        glBindTexture(GL_TEXTURE_2D, pingpongTextures[i]);
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA16F, screenWidth, screenHeight, 0, GL_RGBA, GL_FLOAT, NULL);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
        glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, pingpongTextures[i], 0);
        
        if (glCheckFramebufferStatus(GL_FRAMEBUFFER) != GL_FRAMEBUFFER_COMPLETE) {
            std::cout << "Erreur: Framebuffer ping-pong " << i << " incomplet" << std::endl;
            return false;
        }
    }
    
    // === Framebuffer pour extraction de luminosité ===
    glGenFramebuffers(1, &brightnessFBO);
    glBindFramebuffer(GL_FRAMEBUFFER, brightnessFBO);
    
    glGenTextures(1, &brightnessTexture);
    glBindTexture(GL_TEXTURE_2D, brightnessTexture);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA16F, screenWidth, screenHeight, 0, GL_RGBA, GL_FLOAT, NULL);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, brightnessTexture, 0);
    
    if (glCheckFramebufferStatus(GL_FRAMEBUFFER) != GL_FRAMEBUFFER_COMPLETE) {
        std::cout << "Erreur: Framebuffer de luminosité incomplet" << std::endl;
        return false;
    }
    
    // Revient au framebuffer par défaut
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    
    return true;
}

void PostProcessor::SetupQuadGeometry() {
    glGenVertexArrays(1, &quadVAO);
    glGenBuffers(1, &quadVBO);
    
    glBindVertexArray(quadVAO);
    glBindBuffer(GL_ARRAY_BUFFER, quadVBO);
    glBufferData(GL_ARRAY_BUFFER, sizeof(quadVertices), quadVertices, GL_STATIC_DRAW);
    
    // Position
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)0);
    
    // Coordonnées de texture
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)(2 * sizeof(float)));
    
    glBindVertexArray(0);
}

void PostProcessor::Cleanup() {
    // Framebuffers
    if (sceneFBO != 0) {
        glDeleteFramebuffers(1, &sceneFBO);
        glDeleteTextures(1, &sceneColorTexture);
        glDeleteTextures(1, &sceneDepthTexture);
    }
    
    if (pingpongFBO[0] != 0) {
        glDeleteFramebuffers(2, pingpongFBO);
        glDeleteTextures(2, pingpongTextures);
    }
    
    if (brightnessFBO != 0) {
        glDeleteFramebuffers(1, &brightnessFBO);
        glDeleteTextures(1, &brightnessTexture);
    }
    
    // Géométrie
    if (quadVAO != 0) {
        glDeleteVertexArrays(1, &quadVAO);
        glDeleteBuffers(1, &quadVBO);
    }
    
    // Shaders
    screenShader.reset();
    blurShader.reset();
    bloomShader.reset();
    brightnessShader.reset();
    combineShader.reset();
    
    isInitialized = false;
}

bool PostProcessor::LoadShaders() {
    try {
        // Shader d'écran de base
        screenShader = std::make_unique<Shader>("shaders/screen.vert", "shaders/screen.frag");

        // Shader de flou
        blurShader = std::make_unique<Shader>("shaders/screen.vert", "shaders/blur.frag");

        // Shader de bloom
        bloomShader = std::make_unique<Shader>("shaders/screen.vert", "shaders/bloom.frag");

        // Shader d'extraction de luminosité
        brightnessShader = std::make_unique<Shader>("shaders/screen.vert", "shaders/brightness.frag");

        // Shader de combinaison
        combineShader = std::make_unique<Shader>("shaders/screen.vert", "shaders/combine.frag");

        return true;
    } catch (const std::exception& e) {
        std::cout << "Erreur lors du chargement des shaders: " << e.what() << std::endl;
        return false;
    }
}

void PostProcessor::ApplyBlur(bool horizontal, float intensity) {
    if (!blurShader) return;

    // Utilise les framebuffers ping-pong
    GLuint sourceTexture = horizontal ? sceneColorTexture : pingpongTextures[0];
    GLuint targetFBO = horizontal ? pingpongFBO[0] : pingpongFBO[1];

    glBindFramebuffer(GL_FRAMEBUFFER, targetFBO);
    glDisable(GL_DEPTH_TEST);

    blurShader->use();
    blurShader->setInt("image", 0);
    blurShader->setBool("horizontal", horizontal);
    blurShader->setFloat("intensity", intensity);

    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, sourceTexture);

    glBindVertexArray(quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);

    glEnable(GL_DEPTH_TEST);
    glBindFramebuffer(GL_FRAMEBUFFER, 0);

    // Met à jour la texture de scène avec le résultat
    if (!horizontal) {
        sceneColorTexture = pingpongTextures[1];
    }
}

void PostProcessor::ApplyBloom(float intensity) {
    if (!brightnessShader || !blurShader || !combineShader) return;

    // 1. Extrait les zones lumineuses
    ExtractBrightness(1.0f);

    // 2. Applique un flou à l'extraction
    GLuint originalScene = sceneColorTexture;
    sceneColorTexture = brightnessTexture;

    ApplyBlur(true, intensity);
    ApplyBlur(false, intensity);

    // 3. Combine la scène originale avec le bloom
    CombineTextures(originalScene, pingpongTextures[1], 0.8f);
}

void PostProcessor::ExtractBrightness(float threshold) {
    if (!brightnessShader) return;

    glBindFramebuffer(GL_FRAMEBUFFER, brightnessFBO);
    glDisable(GL_DEPTH_TEST);

    brightnessShader->use();
    brightnessShader->setInt("scene", 0);
    brightnessShader->setFloat("threshold", threshold);

    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, sceneColorTexture);

    glBindVertexArray(quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);

    glEnable(GL_DEPTH_TEST);
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
}

void PostProcessor::CombineTextures(GLuint texture1, GLuint texture2, float blend) {
    if (!combineShader) return;

    glBindFramebuffer(GL_FRAMEBUFFER, pingpongFBO[0]);
    glDisable(GL_DEPTH_TEST);

    combineShader->use();
    combineShader->setInt("texture1", 0);
    combineShader->setInt("texture2", 1);
    combineShader->setFloat("blend", blend);

    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, texture1);
    glActiveTexture(GL_TEXTURE1);
    glBindTexture(GL_TEXTURE_2D, texture2);

    glBindVertexArray(quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);

    glEnable(GL_DEPTH_TEST);
    glBindFramebuffer(GL_FRAMEBUFFER, 0);

    // Met à jour la texture de scène
    sceneColorTexture = pingpongTextures[0];
}

void PostProcessor::RenderQuad(GLuint textureID) {
    glDisable(GL_DEPTH_TEST);

    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, textureID);

    glBindVertexArray(quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);

    glEnable(GL_DEPTH_TEST);
}
