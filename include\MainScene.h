#ifndef MAINSCENE_H
#define MAINSCENE_H

#include "Scene.h"
#include "Shader.h"
#include "Model.h"
#include "Sphere.h"
#include "AudioSource.h"
#include "Sound.h"
#include "Skybox.h"
#include "PostProcessor.h"
#include "UBO.h"
#include "Mat4.h"
#include <memory>
#include <glm/glm.hpp>

/**
 * @brief Scène principale contenant tous les objets 3D et effets
 * 
 * Cette scène représente la scène complète avec tous les modèles,
 * sphères, effets de lumière et sons d'ambiance.
 */
class MainScene : public Scene {
private:
    // === Shaders ===
    std::unique_ptr<Shader> simpleShader;
    std::unique_ptr<Shader> phongShader;
    std::unique_ptr<Shader> texturedShader;
    std::unique_ptr<Shader> sunShader;
    std::unique_ptr<Shader> metalShader;
    std::unique_ptr<Shader> lambertShader;    // Nouveau shader Lambert
    std::unique_ptr<Shader> envmapShader;     // Nouveau shader environment mapping

    // === Modèles 3D ===
    std::unique_ptr<Model> myModel;
    std::unique_ptr<Model> asteroid1;
    std::unique_ptr<Model> asteroid2;
    std::unique_ptr<Model> asteroid3;
    std::unique_ptr<Model> asteroid4;

    // === Sphères ===
    std::unique_ptr<Sphere> moonSphere;
    std::unique_ptr<Sphere> sunSphere;

    // === Audio ===
    std::shared_ptr<Sound> zooSound;
    std::shared_ptr<AudioSource> ambientSource;

    // === Nouvelles fonctionnalités ===
    std::unique_ptr<Skybox> skybox;           // Système de skybox
    std::unique_ptr<PostProcessor> postProcessor; // Post-traitement

    // === Variables de scène ===
    float sunRadius;
    bool initialized;

    // === Options de rendu ===
    enum class ShadingMode {
        PHONG,
        LAMBERT,
        ENVIRONMENT_MAPPING
    };
    ShadingMode currentShadingMode;
    bool usePostProcessing;
    std::vector<PostProcessEffect> activeEffects;

    // === Méthodes privées ===
    bool LoadShaders();
    bool LoadModels();
    bool LoadAudio(SoundManager& soundManager);
    bool InitializeNewFeatures(int screenWidth, int screenHeight);
    void RenderObjects(Camera& camera, int screenWidth, int screenHeight);
    void RenderObjectWithShading(Model& model, const glm::mat4& modelMatrix, Camera& camera, int screenWidth, int screenHeight);
    void RenderAudioUI(GLFWwindow* window, SoundManager& soundManager);
    void RenderKeyboardUI(GLFWwindow* window);
    void RenderShadingUI();
    void RenderPostProcessUI();

public:
    /**
     * @brief Constructeur
     */
    MainScene();

    /**
     * @brief Destructeur
     */
    virtual ~MainScene();

    // === Méthodes héritées de Scene ===
    virtual bool Initialize(Camera& camera, SoundManager& soundManager) override;
    virtual void Update(float deltaTime, GLFWwindow* window, Camera& camera, SoundManager& soundManager) override;
    virtual void Render(Camera& camera, int screenWidth, int screenHeight) override;
    virtual void RenderUI(GLFWwindow* window, SoundManager& soundManager) override;
    virtual void Cleanup() override;
    virtual const char* GetName() const override;
    virtual void OnActivate() override;
    virtual void OnDeactivate() override;

    // === Nouvelles méthodes publiques ===
    void SetShadingMode(ShadingMode mode) { currentShadingMode = mode; }
    ShadingMode GetShadingMode() const { return currentShadingMode; }
    void TogglePostProcessing() { usePostProcessing = !usePostProcessing; }
    bool IsPostProcessingEnabled() const { return usePostProcessing; }
};

#endif // MAINSCENE_H
